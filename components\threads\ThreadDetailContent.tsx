import type { Thread } from '@/api/services/threadsService';
import ImageViewer from '@/components/common/ImageViewer';
import { buildImageUrl } from '@/utils/imageUtils';
import React, { useState } from 'react';
import {
  Dimensions,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import MentionText from './MentionText';


const { width: screenWidth } = Dimensions.get('window');

interface ThreadDetailContentProps {
  thread: Thread;
}

export default function ThreadDetailContent({
  thread
}: ThreadDetailContentProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [imageViewerIndex, setImageViewerIndex] = useState(0);
  
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / screenWidth); // 使用完整屏幕宽度计算
    setCurrentImageIndex(index);
  };

  const openImageViewer = (index: number) => {
    setImageViewerIndex(index);
    setImageViewerVisible(true);
  };

  const closeImageViewer = () => {
    setImageViewerVisible(false);
  };

  const renderImageCarousel = () => {
    if (!thread.images || thread.images.length === 0) return null;

    return (
      <View style={styles.imageCarouselContainer}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          style={styles.imageScrollView}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {thread.images.map((image, index) => (
            <View key={index} style={styles.imageSlide}>
              <TouchableOpacity
                onPress={() => openImageViewer(index)}
                activeOpacity={0.9}
              >
                <Image
                  source={{ uri: buildImageUrl(image) }}
                  style={styles.carouselImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
        
        {/* 图片指示器 */}
        {thread.images.length > 1 && (
          <View style={styles.imageIndicatorContainer}>
            <View style={styles.imageIndicator}>
              <Text style={styles.imageIndicatorText}>
                {currentImageIndex + 1} / {thread.images.length}
              </Text>
            </View>
          </View>
        )}

        {/* 圆点指示器 */}
        {thread.images.length > 1 && (
          <View style={styles.dotsContainer}>
            {thread.images.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === currentImageIndex && styles.activeDot
                ]}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* 图片轮播 */}
      {renderImageCarousel()}

      {/* 标题 */}
      {thread.title && (
        <Text style={styles.title}>{thread.title}</Text>
      )}

      {/* 内容 */}
      <MentionText style={styles.content}>{thread.content}</MentionText>

      {/* 图片查看器 */}
      <ImageViewer
        visible={imageViewerVisible}
        images={(thread.images || []).map(img => buildImageUrl(img))}
        initialIndex={imageViewerIndex}
        onClose={closeImageViewer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 12,
    lineHeight: 24,
  },
  imageCarouselContainer: {
    marginLeft: -16, // 负边距，突破左侧padding
    marginRight: -16, // 负边距，突破右侧padding
    marginTop: -16, // 负边距，让图片贴近容器顶部
    marginBottom: 16,
    position: 'relative',
  },
  imageScrollView: {
    // 移除borderRadius，让图片完全铺满
  },
  imageSlide: {
    width: screenWidth, // 使用完整屏幕宽度
  },
  carouselImage: {
    width: '100%',
    height: Math.min(300, screenWidth * 0.75), // 动态高度，最大300
    // 移除borderRadius，让图片完全铺满
  },
  imageIndicatorContainer: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  imageIndicator: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  imageIndicatorText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  dotsContainer: {
    position: 'absolute',
    bottom: 12,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 6,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeDot: {
    backgroundColor: '#FFFFFF',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    fontSize: 16,
    lineHeight: 24,
    color: '#212529',
    marginBottom: 16,
  },
});
 