import type { CreateThreadRequest } from '@/api/services/threadsService';
import { uploadThreadImage } from '@/utils/fileSystemUpload';
import { buildImageUrl } from '@/utils/imageUtils';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import MentionInput from './MentionInput';
import {
  ActivityIndicator,
  Alert,
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface CreateThreadModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: CreateThreadRequest) => Promise<void>;
  isLoading?: boolean;
}

export default function CreateThreadModal({
  visible,
  onClose,
  onSubmit,
  isLoading = false,
}: CreateThreadModalProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState<boolean[]>([]);

  const resetForm = () => {
    setTitle('');
    setContent('');
    setImages([]);
    setUploadingImages([]);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSubmit = async () => {
    if (!title.trim()) {
      Alert.alert('提示', '请输入帖子标题');
      return;
    }

    if (!content.trim()) {
      Alert.alert('提示', '请输入帖子内容');
      return;
    }

    if (title.length > 100) {
      Alert.alert('提示', '帖子标题不能超过100字符');
      return;
    }

    if (content.length > 1000) {
      Alert.alert('提示', '帖子内容不能超过1000字符');
      return;
    }

    try {
      await onSubmit({
        title: title.trim(),
        content: content.trim(),
        images: images.length > 0 ? images : undefined,
      });
      
      resetForm();
      onClose();
      Alert.alert('成功', '帖子发布成功！');
    } catch (error: any) {
      Alert.alert('错误', error.message || '发布失败，请重试');
    }
  };

  const uploadImage = async (asset: ImagePicker.ImagePickerAsset) => {
    try {
      // 使用 Expo FileSystem 上传图片
      const result = await uploadThreadImage(asset);

      // 只返回相对路径，不构建完整URL
      // 这样存储到数据库的就是相对路径，避免IP变化导致图片失效
      return result.imageUrl;
    } catch (error) {
      console.error('Upload image error:', error);
      throw error;
    }
  };

  const pickImage = async () => {
    if (images.length >= 9) {
      Alert.alert('提示', '最多只能选择9张图片');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        // 添加上传状态
        const currentIndex = images.length;
        setUploadingImages(prev => [...prev, true]);

        // 先显示本地预览
        setImages(prev => [...prev, asset.uri]);

        try {
          // 上传图片到服务器
          const serverImageUrl = await uploadImage(asset);

          // 更新为服务器URL
          setImages(prev => prev.map((img, index) =>
            index === currentIndex ? serverImageUrl : img
          ));

          // 移除上传状态
          setUploadingImages(prev => prev.map((uploading, index) =>
            index === currentIndex ? false : uploading
          ));

        } catch (error) {
          // 上传失败，移除图片
          setImages(prev => prev.filter((_, index) => index !== currentIndex));
          setUploadingImages(prev => prev.filter((_, index) => index !== currentIndex));
          Alert.alert('错误', '图片上传失败，请重试');
        }
      }
    } catch (error) {
      Alert.alert('错误', '选择图片失败');
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setUploadingImages(prev => prev.filter((_, i) => i !== index));
  };

  const addImageFromUrl = () => {
    Alert.prompt(
      '添加图片',
      '请输入图片链接',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          onPress: (url) => {
            if (url && url.trim()) {
              if (images.length >= 9) {
                Alert.alert('提示', '最多只能选择9张图片');
                return;
              }
              setImages(prev => [...prev, url.trim()]);
            }
          },
        },
      ],
      'plain-text'
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Text style={styles.cancelText}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>发布帖子</Text>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isLoading || !title.trim() || !content.trim()}
            style={[
              styles.submitButton,
              (!title.trim() || !content.trim() || isLoading) && styles.submitButtonDisabled
            ]}
          >
            <Text style={[
              styles.submitText,
              (!title.trim() || !content.trim() || isLoading) && styles.submitTextDisabled
            ]}>
              {isLoading ? '发布中...' : '发布'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 标题输入 */}
          <View style={styles.inputSection}>
            <TextInput
              style={styles.titleInput}
              placeholder="添加标题 *"
              value={title}
              onChangeText={setTitle}
              maxLength={100}
              multiline
            />
            <Text style={styles.charCount}>{title.length}/100</Text>
          </View>

          {/* 内容输入 */}
          <View style={styles.inputSection}>
            <MentionInput
              style={styles.contentInput}
              placeholder="分享你的想法... (输入 @ 可以提及产品或品牌)"
              value={content}
              onChangeText={setContent}
              maxLength={1000}
              multiline
            />
            <Text style={styles.charCount}>{content.length}/1000</Text>
          </View>

          {/* 图片预览 */}
          {images.length > 0 && (
            <View style={styles.imagesContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {images.map((image, index) => (
                  <View key={index} style={styles.imageWrapper}>
                    <Image source={{ uri: buildImageUrl(image) }} style={styles.image} />
                    {uploadingImages[index] && (
                      <View style={styles.uploadingOverlay}>
                        <ActivityIndicator size="small" color="#007AFF" />
                        <Text style={styles.uploadingText}>上传中...</Text>
                      </View>
                    )}
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => removeImage(index)}
                      disabled={uploadingImages[index]}
                    >
                      <Ionicons name="close-circle" size={24} color="#ff4757" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          {/* 添加图片按钮 */}
          <View style={styles.toolsContainer}>
            <TouchableOpacity 
              style={styles.toolButton}
              onPress={pickImage}
              disabled={images.length >= 9}
            >
              <Ionicons name="image-outline" size={24} color="#666" />
              <Text style={styles.toolText}>相册</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.toolButton}
              onPress={addImageFromUrl}
              disabled={images.length >= 9}
            >
              <Ionicons name="link-outline" size={24} color="#666" />
              <Text style={styles.toolText}>链接</Text>
            </TouchableOpacity>
            
            <Text style={styles.imageCountText}>
              {images.length}/9
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#666',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    backgroundColor: '#007AFF',
    borderRadius: 16,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  submitTextDisabled: {
    color: '#999',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  inputSection: {
    marginBottom: 16,
  },
  titleInput: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    minHeight: 50,
    textAlignVertical: 'top',
  },
  contentInput: {
    fontSize: 16,
    color: '#333',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  imagesContainer: {
    marginBottom: 16,
  },
  imageWrapper: {
    marginRight: 8,
    position: 'relative',
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 4,
  },
  toolsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  toolButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
  },
  toolText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  imageCountText: {
    flex: 1,
    textAlign: 'right',
    fontSize: 12,
    color: '#999',
  },
});
