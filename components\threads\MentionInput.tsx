import { searchService } from '@/api/services/searchService';
import { BrandNameRecord, ProductNameRecord } from '@/types/entity';
import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  FlatList,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface MentionInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  multiline?: boolean;
  style?: any;
  maxLength?: number;
  autoFocus?: boolean;
}

interface MentionSuggestion {
  id: string;
  name: string;
  type: 'product' | 'brand';
}

export default function MentionInput({
  value,
  onChangeText,
  placeholder,
  multiline = true,
  style,
  maxLength,
  autoFocus = false,
}: MentionInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionStartIndex, setMentionStartIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // 检测 @ 符号并提取查询
  const detectMention = useCallback((text: string, cursorPosition: number) => {
    const beforeCursor = text.substring(0, cursorPosition);
    const lastAtIndex = beforeCursor.lastIndexOf('@');
    
    if (lastAtIndex === -1) {
      setShowSuggestions(false);
      return;
    }

    // 检查 @ 符号后是否有空格
    const afterAt = beforeCursor.substring(lastAtIndex + 1);
    if (afterAt.includes(' ') || afterAt.includes('\n')) {
      setShowSuggestions(false);
      return;
    }

    // 提取查询字符串
    const query = afterAt.trim();
    setMentionQuery(query);
    setMentionStartIndex(lastAtIndex);
    setShowSuggestions(true);

    // 如果查询长度大于0，开始搜索
    if (query.length > 0) {
      searchMentions(query);
    } else {
      setSuggestions([]);
    }
  }, []);

  // 搜索产品和品牌
  const searchMentions = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchService.searchPreview(query);
      const mentionSuggestions: MentionSuggestion[] = [
        ...results.products.map((product: ProductNameRecord) => ({
          id: product.id || product._id,
          name: product.name,
          type: 'product' as const,
        })),
        ...results.brands.map((brand: BrandNameRecord) => ({
          id: brand.id || brand._id,
          name: brand.name,
          type: 'brand' as const,
        })),
      ];
      setSuggestions(mentionSuggestions.slice(0, 8)); // 限制显示8个建议
    } catch (error) {
      console.error('Mention search failed:', error);
      setSuggestions([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // 处理文本变化
  const handleTextChange = useCallback((text: string) => {
    onChangeText(text);
    
    // 获取当前光标位置（简化处理，假设在文本末尾）
    const cursorPosition = text.length;
    detectMention(text, cursorPosition);
  }, [onChangeText, detectMention]);

  // 选择提及项
  const handleSelectMention = useCallback((suggestion: MentionSuggestion) => {
    if (mentionStartIndex === -1) return;

    const beforeMention = value.substring(0, mentionStartIndex);
    const afterMention = value.substring(mentionStartIndex + mentionQuery.length + 1);
    
    // 构建提及文本
    const mentionText = `@${suggestion.type}:${suggestion.name}`;
    const newText = beforeMention + mentionText + ' ' + afterMention;
    
    onChangeText(newText);
    setShowSuggestions(false);
    setSuggestions([]);
    setMentionQuery('');
    setMentionStartIndex(-1);

    // 重新聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, [value, mentionStartIndex, mentionQuery, onChangeText]);

  // 渲染建议项
  const renderSuggestion = useCallback(({ item }: { item: MentionSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSelectMention(item)}
    >
      <Ionicons
        name={item.type === 'product' ? 'cube-outline' : 'business-outline'}
        size={20}
        color="#666"
        style={styles.suggestionIcon}
      />
      <View style={styles.suggestionContent}>
        <Text style={styles.suggestionName}>{item.name}</Text>
        <Text style={styles.suggestionType}>
          {item.type === 'product' ? '产品' : '品牌'}
        </Text>
      </View>
    </TouchableOpacity>
  ), [handleSelectMention]);

  return (
    <View style={styles.container}>
      <TextInput
        ref={inputRef}
        style={[styles.input, style]}
        value={value}
        onChangeText={handleTextChange}
        placeholder={placeholder}
        multiline={multiline}
        maxLength={maxLength}
        autoFocus={autoFocus}
        textAlignVertical="top"
      />
      
      {showSuggestions && (
        <View style={styles.suggestionsContainer}>
          {isSearching ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>搜索中...</Text>
            </View>
          ) : suggestions.length > 0 ? (
            <FlatList
              data={suggestions}
              renderItem={renderSuggestion}
              keyExtractor={(item) => `${item.type}-${item.id}`}
              style={styles.suggestionsList}
              keyboardShouldPersistTaps="handled"
            />
          ) : mentionQuery.length > 0 ? (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>未找到相关产品或品牌</Text>
            </View>
          ) : null}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    maxHeight: 200,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionIcon: {
    marginRight: 12,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  suggestionType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 14,
  },
  noResultsContainer: {
    padding: 16,
    alignItems: 'center',
  },
  noResultsText: {
    color: '#666',
    fontSize: 14,
  },
});
