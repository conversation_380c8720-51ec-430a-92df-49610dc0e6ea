import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';

interface MentionTextProps {
  children: string;
  style?: any;
  numberOfLines?: number;
}

interface MentionPart {
  text: string;
  isMention: boolean;
  mentionType?: 'product' | 'brand';
  mentionName?: string;
}

export default function MentionText({ children, style, numberOfLines }: MentionTextProps) {
  // 解析文本中的提及
  const parseMentions = (text: string): MentionPart[] => {
    const parts: MentionPart[] = [];
    const mentionRegex = /@(product|brand):([^@\s]+)/g;
    let lastIndex = 0;
    let match;

    while ((match = mentionRegex.exec(text)) !== null) {
      // 添加提及前的普通文本
      if (match.index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, match.index),
          isMention: false,
        });
      }

      // 添加提及部分
      parts.push({
        text: match[0], // 完整的提及文本，如 "@product:皇家猫粮"
        isMention: true,
        mentionType: match[1] as 'product' | 'brand',
        mentionName: match[2],
      });

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的普通文本
    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        isMention: false,
      });
    }

    return parts;
  };

  // 处理提及点击
  const handleMentionPress = (mentionType: 'product' | 'brand', mentionName: string) => {
    // 这里可以根据提及类型导航到相应的页面
    // 由于我们没有具体的ID，这里使用搜索页面作为替代
    router.push({
      pathname: '/(stack)/search',
      params: { query: mentionName }
    });
  };

  const parts = parseMentions(children);

  return (
    <Text style={style} numberOfLines={numberOfLines}>
      {parts.map((part, index) => {
        if (part.isMention && part.mentionType && part.mentionName) {
          return (
            <TouchableOpacity
              key={index}
              onPress={() => handleMentionPress(part.mentionType!, part.mentionName!)}
              style={styles.mentionContainer}
            >
              <Text style={styles.mentionText}>
                @{part.mentionName}
              </Text>
            </TouchableOpacity>
          );
        } else {
          return (
            <Text key={index} style={style}>
              {part.text}
            </Text>
          );
        }
      })}
    </Text>
  );
}

const styles = StyleSheet.create({
  mentionContainer: {
    display: 'inline-flex',
  },
  mentionText: {
    color: '#007AFF',
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
});
